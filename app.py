from flask import Flask, render_template, request, redirect, url_for, flash, send_file, session, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
import os
import pandas as pd
import numpy as np
from config import Config
import uuid
from datetime import datetime
import io
import csv

# Initialize the Flask application
app = Flask(__name__)
app.config.from_object(Config)

# Initialize the database
db = SQLAlchemy(app)

# Initialize login manager
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# ----------------------------
# DATABASE MODELS
# ----------------------------

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    password = db.Column(db.String(100), nullable=False)
    company = db.Column(db.String(100))
    mobile = db.Column(db.String(20))
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class UploadedFile(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    original_name = db.Column(db.String(255), nullable=False)
    upload_date = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='uploaded')  # uploaded, processing, completed, failed
    error_message = db.Column(db.Text)  # Store error details for failed files

class CatalogProfile(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    asset_class_type_id = db.Column(db.String(50))
    location_id = db.Column(db.String(50), nullable=False)
    catalog_profile = db.Column(db.String(50), nullable=False)
    catalog_profile_description = db.Column(db.String(100), nullable=False)
    catalog = db.Column(db.String(10), nullable=False)
    code_group = db.Column(db.String(50), nullable=False)
    code_group_description = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(50), nullable=False)
    code_description = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class ComponentCode(db.Model):
    """Stores component codes and their similar names for reuse"""
    id = db.Column(db.Integer, primary_key=True)
    component_name = db.Column(db.String(100), nullable=False, unique=True)
    object_part_code = db.Column(db.String(4), nullable=False)
    similarities = db.Column(db.Text)  # JSON string of similar names
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# ----------------------------
# MACHINE LEARNING FOR CODE GENERATION
# ----------------------------

# Machine Learning for Object Part Code Generation
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.neighbors import NearestNeighbors
import joblib
import os
import json

# Path to save the ML model
ML_MODEL_PATH = os.path.join(os.path.dirname(__file__), 'component_code_model.pkl')
SIMILARITY_THRESHOLD = 0.7  # Threshold for considering names similar

def train_component_code_model():
    """Train a model to suggest object part codes based on component names"""
    # Get all component codes from database
    components = ComponentCode.query.all()
    
    if not components:
        return None
    
    # Prepare data
    names = [c.component_name for c in components]
    codes = [c.object_part_code for c in components]
    
    # Create feature vectors from component names
    vectorizer = CountVectorizer(analyzer='char', ngram_range=(2, 3))
    X = vectorizer.fit_transform(names)
    
    # Train a nearest neighbors model to find similar component names
    model = NearestNeighbors(n_neighbors=3, metric='cosine')
    model.fit(X)
    
    # Save the model and vectorizer
    joblib.dump({
        'vectorizer': vectorizer,
        'model': model,
        'names': names,
        'codes': codes
    }, ML_MODEL_PATH)
    
    return model

def load_component_code_model():
    """Load the trained model if it exists"""
    if not os.path.exists(ML_MODEL_PATH):
        return None
    
    try:
        return joblib.load(ML_MODEL_PATH)
    except:
        return None

def suggest_object_part_code(component_name):
    """
    Use ML model to suggest an object part code for a new component name
    Returns: (suggested_code, is_similar, similar_component)
    """
    model_data = load_component_code_model()
    if not model_data or not model_data['names']:
        # Fallback to simple method if no model
        return generate_object_part_code(component_name), False, None
    
    # Transform the component name
    X = model_data['vectorizer'].transform([component_name])
    
    # Find nearest neighbors
    distances, indices = model_data['model'].kneighbors(X)
    
    # Check if any neighbor is similar enough
    if distances[0][0] < (1 - SIMILARITY_THRESHOLD):
        similar_idx = indices[0][0]
        return model_data['codes'][similar_idx], True, model_data['names'][similar_idx]
    
    # No similar component found - generate a new code
    return generate_object_part_code(component_name), False, None

def update_component_code_model():
    """Retrain the model after changes to component codes"""
    train_component_code_model()

def generate_object_part_code(component_name):
    """
    Generate a 4-character code for a component name.
    Uses ML model if available, otherwise falls back to simple method.
    """
    if not component_name or not isinstance(component_name, str):
        return "XXXX"
    
    # Try ML-based suggestion first
    suggested_code, is_similar, similar_component = suggest_object_part_code(component_name)
    
    if is_similar:
        # If we found a similar component, use its code
        return suggested_code
    
    # Clean the component name
    clean_name = ''.join(e for e in component_name if e.isalnum()).upper()
    
    # If we already have this component in our database, use its code
    existing = ComponentCode.query.filter_by(component_name=component_name).first()
    if existing:
        return existing.object_part_code
    
    # Try to generate a meaningful 4-character code
    if len(clean_name) >= 4:
        # Take first and last characters plus 2 from middle
        code = clean_name[0] + clean_name[1] + clean_name[-2] + clean_name[-1]
    else:
        # If name is short, pad with X's
        code = (clean_name + 'XXXX')[:4]
    
    return code[:4].upper()  # Ensure it's exactly 4 characters

# ----------------------------
# USER LOADER FOR LOGIN MANAGER
# ----------------------------

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# ----------------------------
# HELPER FUNCTIONS
# ----------------------------

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def normalize_column_name(col_name):
    """Normalize column names to handle spaces, case, and special characters"""
    if not isinstance(col_name, str):
        return ""
    return col_name.strip().lower().replace(" ", "").replace("_", "")

def process_catalog_data(df, user_id):
    """
    Process uploaded data through all the steps described in your documentation
    This implements Steps 1-10 from your knowledge base
    """
    # Step 0: Verify required columns are present (with normalization)
    required_columns = ['Asset Class Type ID', 'Location ID', 'Location Name', 'Maintainable Item Name', 
                        'Component Name', 'Failure Mechanism', 'Failure Cause']
    
    # Create normalized version of required columns
    normalized_required = [normalize_column_name(col) for col in required_columns]
    
    # Check actual columns in the dataframe (normalized)
    actual_columns = [normalize_column_name(col) for col in df.columns]
    
    # Find missing columns
    missing_columns = []
    for i, req_col in enumerate(normalized_required):
        if req_col not in actual_columns:
            missing_columns.append(required_columns[i])
    
    if missing_columns:
        error_msg = "Missing required columns: " + ", ".join(missing_columns)
        error_msg += "\n\nYour file must contain these columns:\n" + "\n".join(required_columns)
        error_msg += f"\n\nActual columns found: {', '.join(df.columns)}"
        raise ValueError(error_msg)
    
    # Step 1: Initial data cleaning and preparation
    # Remove duplicates based on all columns
    initial_count = len(df)
    df = df.drop_duplicates()
    duplicates_removed = initial_count - len(df)
    
    # Sort by Location ID, then Maintainable Item Name
    df = df.sort_values(by=['Location ID', 'Maintainable Item Name'])
    
    # Step 2: Add Catalog Profile column
    if 'Catalog Profile' not in df.columns:
        df['Catalog Profile'] = df['Location ID'].apply(
            lambda x: x[:6] + '01' if isinstance(x, str) and len(x) >= 6 else 'UNKNOWN'
        )
    
    # Step 3: Process Failure Mechanism for standardization
    if 'Failure Mechanism' in df.columns and not df['Failure Mechanism'].empty:
        # Remove duplicates and sort
        mechanisms = df['Failure Mechanism'].dropna().unique()
        mechanisms = sorted(mechanisms)
        
        # Process each mechanism for standardization
        standardized_mechanisms = {}
        similarities = {}
        
        for mech in mechanisms:
            if not isinstance(mech, str) or not mech.strip():
                continue
                
            clean_mech = mech.strip().lower()
            
            # Check for obvious similarities
            if 'wear' in clean_mech:
                standardized = 'Wear'
            elif 'leak' in clean_mech:
                standardized = 'Leakage'
            elif 'broken' in clean_mech or 'crack' in clean_mech:
                standardized = 'Cracked'
            elif 'loose' in clean_mech:
                standardized = 'Loose'
            else:
                # Keep first word as base
                standardized = mech.split()[0]
            
            # Store the mapping
            standardized_mechanisms[mech] = standardized
            
            # Track similarities
            if standardized not in similarities:
                similarities[standardized] = []
            if mech not in similarities[standardized]:
                similarities[standardized].append(mech)
        
        # Update the dataframe with standardized mechanisms
        df['Standardized Mechanism'] = df['Failure Mechanism'].map(standardized_mechanisms)
        
        # Save similarities to database for future reuse
        for standard, similar_list in similarities.items():
            # Check if we already have this standard mechanism
            existing = ComponentCode.query.filter_by(component_name=standard).first()
            if not existing:
                # Create new entry with the standard name
                new_code = ComponentCode(
                    component_name=standard,
                    object_part_code=generate_object_part_code(standard),
                    similarities=str(similar_list)
                )
                db.session.add(new_code)
            else:
                # Update existing entry with new similarities
                existing_similarities = eval(existing.similarities) if existing.similarities else []
                for item in similar_list:
                    if item not in existing_similarities:
                        existing_similarities.append(item)
                existing.similarities = str(existing_similarities)
    
    # Step 4: Process Failure Cause (similar to Step 3)
    if 'Failure Cause' in df.columns and not df['Failure Cause'].empty:
        # Similar processing as for mechanisms
        causes = df['Failure Cause'].dropna().unique()
        causes = sorted(causes)
        
        standardized_causes = {}
        cause_similarities = {}
        
        for cause in causes:
            if not isinstance(cause, str) or not cause.strip():
                continue
                
            clean_cause = cause.strip().lower()
            
            # Check for obvious similarities
            if 'lubrication' in clean_cause:
                standardized = 'Lack of Lubrication'
            elif 'corrosion' in clean_cause or 'rust' in clean_cause:
                standardized = 'Corrosion'
            elif 'fatigue' in clean_cause:
                standardized = 'Fatigue'
            else:
                # Keep first word as base
                standardized = cause.split()[0]
            
            standardized_causes[cause] = standardized
            
            # Track similarities
            if standardized not in cause_similarities:
                cause_similarities[standardized] = []
            if cause not in cause_similarities[standardized]:
                cause_similarities[standardized].append(cause)
        
        # Update the dataframe
        df['Standardized Cause'] = df['Failure Cause'].map(standardized_causes)
        
        # Save to database
        for standard, similar_list in cause_similarities.items():
            existing = ComponentCode.query.filter_by(component_name=standard).first()
            if not existing:
                new_code = ComponentCode(
                    component_name=standard,
                    object_part_code=generate_object_part_code(standard),
                    similarities=str(similar_list)
                )
                db.session.add(new_code)
            else:
                existing_similarities = eval(existing.similarities) if existing.similarities else []
                for item in similar_list:
                    if item not in existing_similarities:
                        existing_similarities.append(item)
                existing.similarities = str(existing_similarities)
    
    # Step 5: Generate Damage Codes
    if 'Standardized Mechanism' in df.columns:
        # Create Damage Code Group (first 3 letters of standardized mechanism)
        df['Damage Code Group'] = df['Standardized Mechanism'].apply(
            lambda x: (x[:3].upper() + '01') if isinstance(x, str) and x else ''
        )
        
        # Create Damage Codes (D + sequential number)
        unique_mechanisms = df['Standardized Mechanism'].dropna().unique()
        damage_code_map = {}
        for i, mech in enumerate(unique_mechanisms, 1):
            # Format as D001, D002, etc.
            damage_code = f"D{str(i).zfill(3)}"
            damage_code_map[mech] = damage_code
        
        df['Damage Code'] = df['Standardized Mechanism'].map(damage_code_map)
    
    # Step 6: Generate Object Part Codes
    if 'Component Name' in df.columns:
        df['Object Part Code'] = df['Component Name'].apply(
            lambda x: generate_object_part_code(x) if isinstance(x, str) and x else ''
        )
    
    # Step 7: Prepare for final load sheet
    final_columns = {
        'Asset Class Type ID': 'asset_class_type_id',
        'Location ID': 'location_id',
        'Catalog Profile': 'catalog_profile',
        'Location Name': 'catalog_profile_description',
        'Catalog': 'catalog',  # This would be determined by your business rules
        'Damage Code Group': 'code_group',
        'Component Name': 'code_group_description',
        'Damage Code': 'code',
        'Standardized Mechanism': 'code_description'
    }
    
    # Create final dataframe with only the columns we need
    final_df = df[[col for col in final_columns.keys() if col in df.columns]].copy()
    
    # Rename columns to match expected output
    final_df = final_df.rename(columns=final_columns)
    
    # Add default values for missing columns
    for col in ['catalog', 'code_group', 'code', 'code_description']:
        if col not in final_df.columns:
            final_df[col] = ''
    
    # Step 8-10: Final sorting and duplicate removal
    # Sort by Location ID, then by Catalog (B first, then C, then 5)
    if not final_df.empty:
        # Add a sorting helper column for catalog order
        catalog_order = {'B': 1, 'C': 2, '5': 3}
        final_df['catalog_sort'] = final_df['catalog'].map(catalog_order).fillna(4)
        
        # Sort
        final_df = final_df.sort_values(by=['location_id', 'catalog_sort'])
        
        # Remove duplicates based on all columns
        final_df = final_df.drop_duplicates()
        
        # Drop the helper column
        final_df = final_df.drop('catalog_sort', axis=1)
    
    # Save to database
    for _, row in final_df.iterrows():
        catalog = CatalogProfile(
            user_id=user_id,
            asset_class_type_id=row['asset_class_type_id'],
            location_id=row['location_id'],
            catalog_profile=row['catalog_profile'],
            catalog_profile_description=row['catalog_profile_description'],
            catalog=row['catalog'],
            code_group=row['code_group'],
            code_group_description=row['code_group_description'],
            code=row['code'],
            code_description=row['code_description']
        )
        db.session.add(catalog)
    
    db.session.commit()
    
    # Update ML model with new component codes
    update_component_code_model()
    
    return final_df

# ----------------------------
# ROUTES (WEB PAGES)
# ----------------------------

@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('login.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        
        user = User.query.filter_by(email=email).first()
        
        # For simplicity in this demo, we're using plain text passwords
        # In a real app, you'd use password hashing
        if user and user.password == password:
            login_user(user)
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid email or password', 'error')
    
    return render_template('login.html')

@app.route('/signup', methods=['GET', 'POST'])
def signup():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        email = request.form.get('email')
        password = request.form.get('password')
        company = request.form.get('company', '')
        mobile = request.form.get('mobile', '')
        
        # Check if user already exists
        if User.query.filter_by(email=email).first():
            flash('Email already registered', 'error')
            return redirect(url_for('signup'))
        
        # Create new user
        new_user = User(
            first_name=first_name,
            last_name=last_name,
            email=email,
            password=password,
            company=company,
            mobile=mobile,
            is_admin=(email == '<EMAIL>')  # Make <EMAIL> an admin
        )
        
        db.session.add(new_user)
        db.session.commit()
        
        flash('Account created successfully! Please login.', 'success')
        return redirect(url_for('login'))
    
    return render_template('signup.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get user's processed files
    files = UploadedFile.query.filter_by(user_id=current_user.id).order_by(UploadedFile.upload_date.desc()).all()
    
    # Get catalog profiles
    catalog_profiles = CatalogProfile.query.filter_by(user_id=current_user.id).all()
    
    return render_template('dashboard.html', 
                          files=files, 
                          catalog_profiles=catalog_profiles,
                          is_admin=current_user.is_admin)

@app.route('/upload', methods=['GET', 'POST'])
@login_required
def upload():
    if request.method == 'POST':
        # Check if the post request has the file part
        if 'file' not in request.files:
            flash('No file part in the request', 'error')
            return redirect(request.url)
        
        file = request.files['file']
        
        # If user does not select file, browser also
        # submits an empty part without filename
        if file.filename == '':
            flash('No file selected', 'error')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            # Save the file
            filename = str(uuid.uuid4()) + os.path.splitext(file.filename)[1]
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # Record in database
            uploaded_file = UploadedFile(
                user_id=current_user.id,
                filename=filename,
                original_name=file.filename,
                status='uploaded'
            )
            db.session.add(uploaded_file)
            db.session.commit()
            
            # Process the file
            try:
                # Update status
                uploaded_file.status = 'processing'
                db.session.commit()
                
                # Read the file
                if file.filename.endswith('.csv'):
                    df = pd.read_csv(filepath)
                else:  # Excel file
                    df = pd.read_excel(filepath)
                
                # Check column names for potential issues
                column_issues = []
                for col in df.columns:
                    normalized = normalize_column_name(col)
                    if normalized in [normalize_column_name(req) for req in ['Asset Class Type ID', 'Location ID', 'Location Name', 
                                                                          'Maintainable Item Name', 'Component Name', 
                                                                          'Failure Mechanism', 'Failure Cause']]:
                        continue
                    column_issues.append(f"'{col}' → normalized as '{normalized}'")
                
                if column_issues:
                    flash(f"⚠️ Column name normalization applied: {', '.join(column_issues)}", 'info')
                
                # Process the data
                final_df = process_catalog_data(df, current_user.id)
                
                # Update status
                uploaded_file.status = 'completed'
                db.session.commit()
                
                # Count processed records
                record_count = len(final_df)
                flash(f'✅ File processed successfully! {record_count} catalog profiles created.', 'success')
            except Exception as e:
                # Log the error
                error_msg = str(e)
                print(f"Error processing file: {error_msg}")
                
                # Update status with error details
                uploaded_file.status = 'failed'
                uploaded_file.error_message = error_msg
                db.session.commit()
                
                # Provide user-friendly error message
                if "Missing required columns" in error_msg:
                    # Extract the actual columns found from the error message
                    actual_columns = ""
                    if "Actual columns found:" in error_msg:
                        actual_columns = error_msg.split("Actual columns found:")[1].strip()
                    
                    # Create a helpful error message
                    flash_msg = '❌ Error: Your file is missing required columns. '
                    flash_msg += 'Please make sure your file contains all these columns:\n'
                    flash_msg += 'Asset Class Type ID, Location ID, Location Name, Maintainable Item Name, '
                    flash_msg += 'Component Name, Failure Mechanism, Failure Cause\n\n'
                    
                    if actual_columns:
                        flash_msg += f'Your file has these columns: {actual_columns}\n\n'
                        flash_msg += '💡 Tip: Column names must exactly match (including spaces and capitalization)'
                    
                    flash(flash_msg, 'error')
                else:
                    flash(f'❌ Error processing file: {error_msg}', 'error')
            
            return redirect(url_for('dashboard'))
    
    return render_template('upload.html')

@app.route('/export/<file_id>')
@login_required
def export(file_id):
    # Get the file record
    uploaded_file = UploadedFile.query.get_or_404(file_id)
    
    # Check if user owns this file
    if uploaded_file.user_id != current_user.id and not current_user.is_admin:
        flash('You do not have permission to export this file', 'error')
        return redirect(url_for('dashboard'))
    
    # Get catalog profiles for this user
    catalog_profiles = CatalogProfile.query.filter_by(user_id=current_user.id).all()
    
    # Create a dataframe
    data = []
    for profile in catalog_profiles:
        data.append([
            profile.asset_class_type_id,
            profile.location_id,
            profile.catalog_profile,
            profile.catalog_profile_description,
            profile.catalog,
            profile.code_group,
            profile.code_group_description,
            profile.code,
            profile.code_description
        ])
    
    # Create DataFrame
    df = pd.DataFrame(data, columns=[
        'Asset Class Type ID', 'Location ID', 'Catalog Profile', 'Catalog Profile Description',
        'Catalog', 'Code Group', 'Code Group Description', 'Code', 'Code Description'
    ])
    
    # Create CSV
    output = io.StringIO()
    df.to_csv(output, index=False)
    output.seek(0)
    
    # Return as file
    return send_file(
        io.BytesIO(output.getvalue().encode()),
        mimetype='text/csv',
        as_attachment=True,
        download_name=f'catalog_profiles_{datetime.now().strftime("%Y%m%d")}.csv'
    )

@app.route('/download_template')
@login_required
def download_template():
    """Serve the template file for download"""
    # Create a template file in memory
    template_content = """Asset Class Type ID,Location ID,Location Name,Maintainable Item Name,Component Name,Failure Mechanism,Failure Cause
CRGY,CRGY05-ELIL,Electrical & Interlocks,Junction Box,Enclosure,Loose,Worn Seals
CRGY,CRGY05-STRC,Structure,Bottomshell,Piping,Leaking,Loose Fittings
CRGY,CRGY05-CRCO,Crusher Components,Spider,Vent Cover Breather,Contaminated,Oil Splash Back
"""
    
    # Return as file
    return send_file(
        io.BytesIO(template_content.encode()),
        mimetype='text/csv',
        as_attachment=True,
        download_name='sap_catalog_template.csv'
    )

@app.route('/admin')
@login_required
def admin():
    if not current_user.is_admin:
        flash('You do not have admin access', 'error')
        return redirect(url_for('dashboard'))
    
    # Get all users
    users = User.query.all()
    
    # Get all component codes for ML training
    component_codes = ComponentCode.query.all()
    
    return render_template('admin.html', 
                          users=users,
                          component_codes=component_codes)

@app.route('/admin/edit_component/<int:code_id>', methods=['POST'])
@login_required
def edit_component(code_id):
    if not current_user.is_admin:
        return {'success': False, 'message': 'Unauthorized'}, 403
    
    component = ComponentCode.query.get_or_404(code_id)
    
    new_code = request.form.get('object_part_code', '').strip().upper()
    if len(new_code) != 4:
        return {'success': False, 'message': 'Code must be exactly 4 characters'}, 400
    
    component.object_part_code = new_code
    db.session.commit()
    
    return {'success': True, 'new_code': new_code}

@app.route('/admin/add_component', methods=['POST'])
@login_required
def add_component():
    if not current_user.is_admin:
        return {'success': False, 'message': 'Unauthorized'}, 403
    
    component_name = request.form.get('component_name', '').strip()
    object_part_code = request.form.get('object_part_code', '').strip().upper()
    
    if not component_name or len(object_part_code) != 4:
        return {'success': False, 'message': 'Invalid input'}, 400
    
    # Check if component already exists
    if ComponentCode.query.filter_by(component_name=component_name).first():
        return {'success': False, 'message': 'Component already exists'}, 400
    
    new_component = ComponentCode(
        component_name=component_name,
        object_part_code=object_part_code,
        similarities='[]'
    )
    
    db.session.add(new_component)
    db.session.commit()
    
    # Update ML model
    update_component_code_model()
    
    return {'success': True, 'id': new_component.id}

@app.route('/admin/update_ml_model', methods=['POST'])
@login_required
def update_ml_model():
    if not current_user.is_admin:
        return {'success': False, 'message': 'Unauthorized'}, 403
    
    try:
        update_component_code_model()
        return {'success': True, 'message': 'ML model updated successfully'}
    except Exception as e:
        return {'success': False, 'message': str(e)}, 500

# ----------------------------
# DATABASE MIGRATION
# ----------------------------

def check_and_fix_database():
    """Check if database needs migration and fix if necessary"""
    with app.app_context():
        # Check if database file exists
        db_path = Config.SQLALCHEMY_DATABASE_URI.replace('sqlite:///', '')
        db_exists = os.path.exists(db_path)
        
        if not db_exists:
            print("ℹ️ Database doesn't exist yet. Creating new database...")
            db.create_all()
            return
        
        # Check if error_message column exists
        try:
            # Try to query with the new column
            db.session.query(UploadedFile.error_message).limit(1).all()
            print("✅ Database schema is up to date")
        except Exception as e:
            print(f"⚠️ Database schema needs update: {str(e)}")
            
            # Create engine to execute raw SQL
            from sqlalchemy import create_engine
            engine = create_engine(Config.SQLALCHEMY_DATABASE_URI)
            
            # Add the missing column
            print("🔧 Adding error_message column to uploaded_file table...")
            with engine.connect() as conn:
                conn.execute("ALTER TABLE uploaded_file ADD COLUMN error_message TEXT")
            
            print("✅ Database schema updated successfully")

# ----------------------------
# INITIALIZE DATABASE
# ----------------------------

def init_db():
    with app.app_context():
        # First check if we need to fix the database
        check_and_fix_database()
        
        # Create admin user if doesn't exist
        if not User.query.filter_by(email='<EMAIL>').first():
            admin = User(
                first_name='Admin',
                last_name='User',
                email='<EMAIL>',
                password='admin123',
                is_admin=True
            )
            db.session.add(admin)
            db.session.commit()
            print("✅ Admin user created successfully")
        else:
            print("ℹ️ Admin user already exists")

# Initialize the database
init_db()

# ----------------------------
# RUN THE APPLICATION
# ----------------------------

if __name__ == '__main__':
    print("🚀 Starting SAP Catalog Profile Application...")
    print("👉 Go to http://127.0.0.1:5000 in your browser")
    app.run(debug=True)