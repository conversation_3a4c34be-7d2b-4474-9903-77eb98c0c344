{% extends "base.html" %}

{% block content %}
<h2 class="mb-4">Admin Panel</h2>

<ul class="nav nav-tabs mb-4">
    <li class="nav-item">
        <a class="nav-link active" data-bs-toggle="tab" href="#users">Users</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" data-bs-toggle="tab" href="#component-codes">Component Codes</a>
    </li>
</ul>

<div class="tab-content">
    <!-- Users Tab -->
    <div class="tab-pane fade show active" id="users">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">User Management</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Company</th>
                                <th>Mobile</th>
                                <th>Role</th>
                                <th>Joined</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>{{ user.id }}</td>
                                <td>{{ user.first_name }} {{ user.last_name }}</td>
                                <td>{{ user.email }}</td>
                                <td>{{ user.company or 'N/A' }}</td>
                                <td>{{ user.mobile or 'N/A' }}</td>
                                <td>
                                    {% if user.is_admin %}
                                    <span class="badge bg-danger">Admin</span>
                                    {% else %}
                                    <span class="badge bg-secondary">User</span>
                                    {% endif %}
                                </td>
                                <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if not user.is_admin %}
                                    <button class="btn btn-sm btn-outline-danger make-admin" 
                                            data-user-id="{{ user.id }}">
                                        Make Admin
                                    </button>
                                    {% else %}
                                    <span class="text-muted">Admin</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Component Codes Tab -->
    <div class="tab-pane fade" id="component-codes">
        <div class="card">
            <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Component Codes Management</h5>
                <button class="btn btn-sm btn-outline-dark" data-bs-toggle="modal" data-bs-target="#addComponentModal">
                    Add New Component Code
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Component Name</th>
                                <th>Object Part Code</th>
                                <th>Similar Names</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for code in component_codes %}
                            <tr>
                                <td>{{ code.component_name }}</td>
                                <td id="admin-code-{{ code.id }}">{{ code.object_part_code }}</td>
                                <td>
                                    {% if code.similarities %}
                                        {% set similarities = code.similarities|safe|replace('[','')|replace(']','')|replace("'",'')|split(',') %}
                                        {% for sim in similarities if sim.strip() %}
                                            <span class="badge bg-light text-dark border m-1">{{ sim.strip() }}</span>
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-muted">None</span>
                                    {% endif %}
                                </td>
                                <td>{{ code.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary edit-code" 
                                            data-id="{{ code.id }}"
                                            data-code="{{ code.object_part_code }}">
                                        Edit
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Code Modal -->
<div class="modal fade" id="editCodeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Object Part Code</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editCodeForm">
                <div class="modal-body">
                    <input type="hidden" id="editCodeId" name="code_id">
                    <div class="mb-3">
                        <label class="form-label">Current Code</label>
                        <input type="text" class="form-control" id="currentCode" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">New 4-Character Code</label>
                        <input type="text" class="form-control" id="newCode" name="object_part_code" maxlength="4" required>
                        <div class="form-text">Must be exactly 4 characters (e.g., SPRG, BEAR)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Component Modal -->
<div class="modal fade" id="addComponentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Component Code</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addComponentForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Component Name</label>
                        <input type="text" class="form-control" id="componentName" name="component_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">4-Character Code</label>
                        <input type="text" class="form-control" id="partCode" name="object_part_code" maxlength="4" required>
                        <div class="form-text">Must be exactly 4 characters (e.g., SPRG, BEAR)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Component</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Edit code functionality
        document.querySelectorAll('.edit-code').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const code = this.getAttribute('data-code');
                
                document.getElementById('editCodeId').value = id;
                document.getElementById('currentCode').value = code;
                document.getElementById('newCode').value = code;
                
                new bootstrap.Modal(document.getElementById('editCodeModal')).show();
            });
        });
        
        // Edit form submit handler
        document.getElementById('editCodeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const codeId = formData.get('code_id');
            
            fetch(`/admin/edit_component/${codeId}`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the displayed code
                    document.getElementById(`admin-code-${codeId}`).textContent = data.new_code;
                    bootstrap.Modal.getInstance(document.getElementById('editCodeModal')).hide();
                    alert('Code updated successfully!');
                } else {
                    alert(`Error: ${data.message}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the code.');
            });
        });
        
        // Add component form submit handler
        document.getElementById('addComponentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const componentName = formData.get('component_name');
            const objectPartCode = formData.get('object_part_code');
            
            fetch('/admin/add_component', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    'component_name': componentName,
                    'object_part_code': objectPartCode
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Component added successfully!');
                    location.reload(); // Refresh to see the new component
                } else {
                    alert(`Error: ${data.message}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while adding the component.');
            });
        });
        
        // Make user admin functionality
        document.querySelectorAll('.make-admin').forEach(button => {
            button.addEventListener('click', function() {
                const userId = this.getAttribute('data-user-id');
                
                if (confirm('Are you sure you want to make this user an admin?')) {
                    fetch(`/admin/make_admin/${userId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({make_admin: true})
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('User promoted to admin successfully!');
                            location.reload();
                        } else {
                            alert(`Error: ${data.message}`);
                        }
                    });
                }
            });
        });
    });
</script>
{% endblock %}