{% extends "base.html" %}

{% block content %}
<h2 class="mb-4">Dashboard</h2>

<div class="row">
    <!-- Recent Uploads Card -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Recent Uploads</h5>
            </div>
            <div class="card-body">
                {% if files %}
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>File Name</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for file in files %}
                        <tr>
                            <td>{{ file.original_name }}</td>
                            <td>{{ file.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                {% if file.status == 'uploaded' %}
                                <span class="badge bg-secondary">Uploaded</span>
                                {% elif file.status == 'processing' %}
                                <span class="badge bg-warning text-dark">Processing</span>
                                {% elif file.status == 'completed' %}
                                <span class="badge bg-success">Completed</span>
                                {% else %}
                                <span class="badge bg-danger">Failed</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if file.status == 'completed' %}
                                <a href="{{ url_for('export', file_id=file.id) }}" class="btn btn-sm btn-outline-primary">
                                    Export
                                </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% else %}
                <p class="text-muted">No files uploaded yet.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions Card -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('upload') }}" class="btn btn-lg btn-primary">
                        Upload New Data
                    </a>
                    <a href="{{ url_for('export', file_id='latest') }}" class="btn btn-lg btn-success">
                        Export All Catalog Profiles
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Component Code Management (Admin only) -->
        {% if is_admin %}
        <div class="card mt-4 admin-only">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">Component Code Management</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-sm btn-warning mb-3" data-bs-toggle="modal" data-bs-target="#addComponentModal">
                    Add New Component Code
                </button>
                
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>Component Name</th>
                                <th>Object Part Code</th>
                                <th>Similar Names</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for code in component_codes %}
                            <tr>
                                <td>{{ code.component_name }}</td>
                                <td id="code-{{ code.id }}">{{ code.object_part_code }}</td>
                                <td>
                                    {% if code.similarities %}
                                        {% set similarities = code.similarities|safe|replace('[','')|replace(']','')|replace("'",'')|split(',') %}
                                        {% for sim in similarities if sim.strip() %}
                                            <span class="badge bg-light text-dark border">{{ sim.strip() }}</span>
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-muted">None</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary edit-code" 
                                            data-id="{{ code.id }}"
                                            data-code="{{ code.object_part_code }}">
                                        Edit
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Edit Code Modal -->
<div class="modal fade" id="editCodeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Object Part Code</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editCodeForm">
                <div class="modal-body">
                    <input type="hidden" id="editCodeId" name="code_id">
                    <div class="mb-3">
                        <label class="form-label">Current Code</label>
                        <input type="text" class="form-control" id="currentCode" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">New 4-Character Code</label>
                        <input type="text" class="form-control" id="newCode" name="object_part_code" maxlength="4" required>
                        <div class="form-text">Must be exactly 4 characters (e.g., SPRG, BEAR)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Component Modal -->
<div class="modal fade" id="addComponentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Component Code</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addComponentForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Component Name</label>
                        <input type="text" class="form-control" id="componentName" name="component_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">4-Character Code</label>
                        <input type="text" class="form-control" id="partCode" name="object_part_code" maxlength="4" required>
                        <div class="form-text">Must be exactly 4 characters (e.g., SPRG, BEAR)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Component</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    // Edit code functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Edit button click handler
        document.querySelectorAll('.edit-code').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const code = this.getAttribute('data-code');
                
                document.getElementById('editCodeId').value = id;
                document.getElementById('currentCode').value = code;
                document.getElementById('newCode').value = code;
                
                new bootstrap.Modal(document.getElementById('editCodeModal')).show();
            });
        });
        
        // Edit form submit handler
        document.getElementById('editCodeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const codeId = formData.get('code_id');
            
            fetch(`/admin/edit_component/${codeId}`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the displayed code
                    document.getElementById(`code-${codeId}`).textContent = data.new_code;
                    bootstrap.Modal.getInstance(document.getElementById('editCodeModal')).hide();
                    alert('Code updated successfully!');
                } else {
                    alert(`Error: ${data.message}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the code.');
            });
        });
        
        // Add component form submit handler
        document.getElementById('addComponentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const componentName = formData.get('component_name');
            const objectPartCode = formData.get('object_part_code');
            
            fetch('/admin/add_component', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    'component_name': componentName,
                    'object_part_code': objectPartCode
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Component added successfully!');
                    location.reload(); // Refresh to see the new component
                } else {
                    alert(`Error: ${data.message}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while adding the component.');
            });
        });
    });
</script>
{% endblock %}