{% extends "base.html" %}

{% block content %}
<h2 class="mb-4">Upload Data for Processing</h2>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Upload File</h5>
            </div>
            <div class="card-body">
                <p class="card-text">
                    Upload your equipment/component data file. Supported formats: Excel (.xlsx, .xls) or CSV (.csv).
                </p>
                
                <form method="POST" action="{{ url_for('upload') }}" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="file" class="form-label">Select File</label>
                        <input class="form-control" type="file" id="file" name="file" accept=".xlsx,.xls,.csv" required>
                        <div class="form-text">Maximum file size: 50MB</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h5 class="alert-heading">Required Columns</h5>
                        <p>Your file must contain exactly these columns:</p>
                        <ul>
                            <li>Asset Class Type ID</li>
                            <li>Location ID</li>
                            <li>Location Name</li>
                            <li>Maintainable Item Name</li>
                            <li>Component Name</li>
                            <li>Failure Mechanism</li>
                            <li>Failure Cause</li>
                        </ul>
                        <p class="mb-0">💡 Tip: Download our <a href="{{ url_for('download_template') }}">template file</a> to ensure correct format</p>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Upload and Process</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Template</h5>
            </div>
            <div class="card-body">
                <p class="card-text">
                    Download a template file with the correct columns and format.
                </p>
                <a href="{{ url_for('download_template') }}" class="btn btn-outline-info">Download Template</a>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">Processing Steps</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li>Upload your data file</li>
                    <li>System validates required columns</li>
                    <li>QA verification for similar names</li>
                    <li>Object part code generation</li>
                    <li>Final catalog profile creation</li>
                </ol>
                <p class="text-muted">
                    <small>
                        During processing, similar component names will be grouped together and assigned the same object part code for consistency.
                    </small>
                </p>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">Common Upload Issues</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item">❌ Column name mismatch (check spaces and capitalization)</li>
                    <li class="list-group-item">❌ Incorrect file format (must be Excel or CSV)</li>
                    <li class="list-group-item">❌ Hidden characters in column headers</li>
                    <li class="list-group-item">❌ Special formatting in Excel files</li>
                </ul>
                <p class="mt-3">
                    <strong>Tip:</strong> If you're having issues, try copying your data into our template file.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}